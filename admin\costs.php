<?php
// /admin/costs.php
require_once __DIR__ . '/../includes/header.php';

// Role-based guard for admin
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Location: ../auth/login.php');
  exit;
}

// Fetch costs data with error handling
try {
    $costs = $pdo->query("SELECT co.*, ca.name AS category, su.name AS subcategory FROM costs co LEFT JOIN categories ca ON co.category_id = ca.id LEFT JOIN subcategories su ON co.subcategory_id = su.id ORDER BY ca.name, su.name, co.created_at DESC")->fetchAll();
} catch (Exception $e) {
    $costs = [];
}

// Get total number of learners from revenue data
try {
    $totalLearners = $pdo->query("SELECT SUM(total_students) as total FROM revenue")->fetch()['total'] ?? 0;
} catch (Exception $e) {
    $totalLearners = 0;
}

// Group costs by category and subcategory
$groupedCosts = [];
foreach ($costs as $cost) {
    $category = $cost['category'] ?? 'Uncategorized';
    $subcategory = $cost['subcategory'] ?? 'No Subcategory';

    if (!isset($groupedCosts[$category])) {
        $groupedCosts[$category] = [];
    }

    if (!isset($groupedCosts[$category][$subcategory])) {
        $groupedCosts[$category][$subcategory] = [];
    }

    $groupedCosts[$category][$subcategory][] = $cost;
}
?>

  <main class="container">
    <h1 class="section-title"><i class="fas fa-money-bill-wave"></i> Cost Management</h1>

    <div class="card">
      <div class="card-header">
        <h2><i class="fas fa-plus-circle"></i> Add New Cost</h2>
        <div class="learner-info">
          <span class="learner-count">
            <i class="fas fa-users"></i> Total Learners: <strong><?= number_format($totalLearners) ?></strong>
          </span>
        </div>
      </div>
      <form id="cost-form">
        <div class="form-group">
          <label class="form-label" for="cost-description">Description</label>
          <input type="text" id="cost-description" name="description" placeholder="Enter cost description" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="cost-amount">Amount</label>
          <input type="number" id="cost-amount" name="amount" placeholder="0.00" step="0.01" required>
          <small class="form-help" id="amount-help">Enter the base amount per unit</small>
        </div>
        <div class="form-group">
          <label class="form-label" for="category-select">Category</label>
          <select name="category_id" id="category-select" required>
            <option value="">Select Category</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="subcategory-select">Subcategory</label>
          <select name="subcategory_id" id="subcategory-select" required>
            <option value="">Select Category First</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="rate-type">Rate Type</label>
          <select name="rate_type" id="rate-type">
            <option value="daily">Daily</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="num-days">Number of Days</label>
          <input type="number" id="num-days" name="num_days" placeholder="1" min="1" value="1" required>
        </div>

        <!-- Multiply by Learners Checkbox -->
        <div class="form-group">
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="multiply-by-learners" name="multiply_by_learners" value="1">
              <span class="checkmark"></span>
              <span class="checkbox-text">
                <strong>Multiply by all learners</strong>
                <small>Cost will be multiplied by <?= number_format($totalLearners) ?> learners</small>
              </span>
            </label>
          </div>
        </div>

        <!-- Cost Preview -->
        <div class="cost-preview" id="cost-preview" style="display: none;">
          <div class="preview-content">
            <h4><i class="fas fa-calculator"></i> Cost Preview</h4>
            <div class="preview-breakdown">
              <div class="preview-line">
                <span>Base Amount:</span>
                <span id="preview-base">R0.00</span>
              </div>
              <div class="preview-line">
                <span>Days:</span>
                <span id="preview-days">1</span>
              </div>
              <div class="preview-line" id="preview-learners-line" style="display: none;">
                <span>Learners:</span>
                <span id="preview-learners"><?= number_format($totalLearners) ?></span>
              </div>
              <div class="preview-line preview-total">
                <span><strong>Total Cost:</strong></span>
                <span id="preview-total"><strong>R0.00</strong></span>
              </div>
            </div>
          </div>
        </div>

        <button type="submit" class="btn btn-primary">
          <i class="fas fa-plus"></i> Add Cost
        </button>
      </form>
    </div>

    <h2 class="section-subtitle"><i class="fas fa-list"></i> Saved Costs</h2>

    <?php if (empty($costs)): ?>
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">No Costs Found</h3>
        </div>
        <p class="card-subtitle">Add your first cost using the form above.</p>
      </div>
    <?php else: ?>
      <!-- Category Selector -->
      <div class="card category-selector-card">
        <div class="card-header">
          <h3><i class="fas fa-list"></i> Saved Costs by Category</h3>
        </div>
        <div class="category-selector-content">
          <div class="dropdown-selector-container">
            <label for="category-selector" class="form-label">
              <i class="fas fa-tag"></i> Select Category to View:
            </label>
            <div class="custom-dropdown">
              <button type="button" class="dropdown-btn" id="category-dropdown-btn">
                <span class="dropdown-text">Choose a category...</span>
                <i class="fas fa-chevron-down dropdown-icon"></i>
              </button>
              <div class="dropdown-menu" id="category-dropdown-menu">
                <?php foreach ($groupedCosts as $categoryName => $subcategories): ?>
                  <?php
                    $categoryTotal = 0;
                    $categoryCount = 0;
                    foreach ($subcategories as $subcategoryCosts) {
                      foreach ($subcategoryCosts as $cost) {
                        $categoryTotal += ($cost['amount'] * $cost['num_days']);
                        $categoryCount++;
                      }
                    }
                  ?>
                  <div class="dropdown-item" data-category="<?= htmlspecialchars($categoryName) ?>">
                    <div class="dropdown-item-content">
                      <span class="category-name">
                        <i class="fas fa-tag"></i> <?= htmlspecialchars($categoryName) ?>
                      </span>
                      <span class="category-info">
                        <?= $categoryCount ?> costs • R<?= number_format($categoryTotal, 2) ?>
                      </span>
                    </div>
                  </div>
                <?php endforeach; ?>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Selected Category Content -->
      <div id="selected-category-content" class="card category-content-display" style="display: none;">
        <div class="card-header">
          <h3 id="selected-category-title">
            <i class="fas fa-tag"></i> <span id="selected-category-name"></span>
          </h3>
          <div id="selected-category-summary" class="category-summary">
            <span id="selected-category-stats" class="category-stats"></span>
          </div>
        </div>
        <div id="selected-category-subcategories">
          <!-- Subcategories will be loaded here dynamically -->
        </div>
      </div>

      <!-- Hidden data for JavaScript -->
      <script type="application/json" id="categories-data">
        <?php echo json_encode($groupedCosts); ?>
      </script>
    <?php endif; ?>
  </main>

  <!-- Edit Cost Modal -->
  <div id="edit-cost-modal" class="modal" style="display:none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Edit Cost Entry</h3>
        <span class="close-modal">&times;</span>
      </div>
      <form id="edit-cost-form">
        <input type="hidden" name="id" id="edit-cost-id">
        <div class="form-group">
          <label class="form-label" for="edit-cost-description">Description</label>
          <input type="text" name="description" id="edit-cost-description" placeholder="Description" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-cost-amount">Amount</label>
          <input type="number" name="amount" id="edit-cost-amount" placeholder="Amount" step="0.01" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-cost-days">Number of Days</label>
          <input type="number" name="num_days" id="edit-cost-days" placeholder="Days" min="1" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-cost-rate">Rate Type</label>
          <select name="rate_type" id="edit-cost-rate">
            <option value="daily">Daily</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-save"></i> Save Changes
        </button>
      </form>
    </div>
  </div>

  <script src="../assets/js/edit-handlers-new.js"></script>

  <script>
    // Category dropdown selector functionality
    document.addEventListener('DOMContentLoaded', () => {
      // Get elements
      const dropdownBtn = document.getElementById('category-dropdown-btn');
      const dropdownMenu = document.getElementById('category-dropdown-menu');
      const dropdownItems = document.querySelectorAll('.dropdown-item');
      const selectedCategoryContent = document.getElementById('selected-category-content');
      const selectedCategoryName = document.getElementById('selected-category-name');
      const selectedCategoryStats = document.getElementById('selected-category-stats');
      const selectedCategorySubcategories = document.getElementById('selected-category-subcategories');
      const dropdownText = document.querySelector('.dropdown-text');
      const dropdownIcon = document.querySelector('.dropdown-icon');

      // Get categories data
      const categoriesData = JSON.parse(document.getElementById('categories-data').textContent);

      // Toggle dropdown menu
      function toggleDropdown() {
        const isOpen = dropdownMenu.classList.contains('show');
        if (isOpen) {
          closeDropdown();
        } else {
          openDropdown();
        }
      }

      function openDropdown() {
        dropdownMenu.classList.add('show');
        dropdownBtn.classList.add('active');
      }

      function closeDropdown() {
        dropdownMenu.classList.remove('show');
        dropdownBtn.classList.remove('active');
      }

      // Handle dropdown button click
      dropdownBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        toggleDropdown();
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        if (!dropdownBtn.contains(e.target) && !dropdownMenu.contains(e.target)) {
          closeDropdown();
        }
      });

      // Handle category selection
      dropdownItems.forEach(item => {
        item.addEventListener('click', (e) => {
          e.stopPropagation();

          // Remove previous selection
          dropdownItems.forEach(i => i.classList.remove('selected'));

          // Add selection to clicked item
          item.classList.add('selected');

          // Get selected category
          const categoryName = item.dataset.category;
          const categoryData = categoriesData[categoryName];

          // Update dropdown button text
          dropdownText.textContent = categoryName;
          dropdownBtn.classList.remove('placeholder');

          // Display selected category
          displayCategory(categoryName, categoryData);

          // Close dropdown
          closeDropdown();
        });
      });

      // Function to display selected category
      function displayCategory(categoryName, categoryData) {
        // Update header
        selectedCategoryName.textContent = categoryName;

        // Calculate totals
        let totalCosts = 0;
        let totalAmount = 0;

        Object.values(categoryData).forEach(subcategoryCosts => {
          subcategoryCosts.forEach(cost => {
            totalCosts++;
            totalAmount += parseFloat(cost.amount) * parseInt(cost.num_days);
          });
        });

        selectedCategoryStats.textContent = `${totalCosts} costs • R${totalAmount.toLocaleString('en-ZA', { minimumFractionDigits: 2 })} total`;

        // Generate subcategories HTML
        let subcategoriesHTML = '';

        if (Object.keys(categoryData).length === 0) {
          subcategoriesHTML = `
            <div class="empty-category-state">
              <i class="fas fa-inbox"></i>
              <h4>No costs found</h4>
              <p>This category doesn't have any costs yet. Add some costs using the form above.</p>
            </div>`;
        } else {
          Object.entries(categoryData).forEach(([subcategoryName, subcategoryCosts]) => {
          let subcategoryTotal = 0;
          subcategoryCosts.forEach(cost => {
            subcategoryTotal += parseFloat(cost.amount) * parseInt(cost.num_days);
          });

          subcategoriesHTML += `
            <div class="subcategory-costs-section">
              <div class="subcategory-header">
                <h4 class="subcategory-title">
                  <i class="fas fa-layer-group"></i> ${escapeHtml(subcategoryName)}
                  <span class="subcategory-count">(${subcategoryCosts.length} costs)</span>
                </h4>
              </div>
              <div class="table-container">
                <table class="data-table subcategory-table">
                  <thead>
                    <tr>
                      <th>Description</th>
                      <th>Amount</th>
                      <th>Rate Type</th>
                      <th>Days</th>
                      <th>Total</th>
                      <th>Date Added</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>`;

          subcategoryCosts.forEach(cost => {
            const costTotal = parseFloat(cost.amount) * parseInt(cost.num_days);
            const dateAdded = new Date(cost.created_at).toLocaleDateString('en-ZA', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            });

            subcategoriesHTML += `
              <tr>
                <td><strong>${escapeHtml(cost.description)}</strong></td>
                <td>R${parseFloat(cost.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2 })}</td>
                <td>
                  <span class="rate-badge rate-${cost.rate_type}">
                    ${cost.rate_type.charAt(0).toUpperCase() + cost.rate_type.slice(1)}
                  </span>
                </td>
                <td>${cost.num_days}</td>
                <td><strong>R${costTotal.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}</strong></td>
                <td>${dateAdded}</td>
                <td>
                  <button class="btn btn-sm btn-secondary edit-btn" data-id="${cost.id}" title="Edit Cost">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button class="btn btn-sm btn-danger delete-btn" data-id="${cost.id}" title="Delete Cost">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>`;
          });

          subcategoriesHTML += `
                  </tbody>
                  <tfoot>
                    <tr class="subcategory-total">
                      <td colspan="4"><strong>Subcategory Total:</strong></td>
                      <td><strong>R${subcategoryTotal.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}</strong></td>
                      <td colspan="2"></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>`;
          });
        }

        // Update content
        selectedCategorySubcategories.innerHTML = subcategoriesHTML;

        // Show the content
        selectedCategoryContent.style.display = 'block';

        // Re-attach event listeners for edit/delete buttons
        attachEditDeleteListeners();
      }

      // Helper function to escape HTML
      function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
      }

      // Function to attach edit/delete event listeners
      function attachEditDeleteListeners() {
        // The edit-handlers-new.js will automatically handle these buttons
        // No need to manually attach listeners as they use event delegation
      }

      // Auto-select first category on page load
      if (dropdownItems.length > 0) {
        const firstItem = dropdownItems[0];
        const categoryName = firstItem.dataset.category;
        const categoryData = categoriesData[categoryName];

        // Update UI
        firstItem.classList.add('selected');
        dropdownText.textContent = categoryName;
        dropdownBtn.classList.remove('placeholder');

        // Display the category
        displayCategory(categoryName, categoryData);
      }

      // Cost calculation and preview functionality
      const amountInput = document.getElementById('cost-amount');
      const daysInput = document.getElementById('num-days');
      const multiplyCheckbox = document.getElementById('multiply-by-learners');
      const costPreview = document.getElementById('cost-preview');
      const previewBase = document.getElementById('preview-base');
      const previewDays = document.getElementById('preview-days');
      const previewLearners = document.getElementById('preview-learners');
      const previewLearnersLine = document.getElementById('preview-learners-line');
      const previewTotal = document.getElementById('preview-total');
      const amountHelp = document.getElementById('amount-help');

      const totalLearners = <?= $totalLearners ?>;

      function updateCostPreview() {
        const amount = parseFloat(amountInput.value) || 0;
        const days = parseInt(daysInput.value) || 1;
        const multiplyByLearners = multiplyCheckbox.checked;

        if (amount > 0) {
          costPreview.style.display = 'block';

          // Update preview values
          previewBase.textContent = 'R' + amount.toLocaleString('en-ZA', { minimumFractionDigits: 2 });
          previewDays.textContent = days;

          // Calculate total
          let total = amount * days;
          if (multiplyByLearners) {
            total *= totalLearners;
            previewLearnersLine.style.display = 'flex';
          } else {
            previewLearnersLine.style.display = 'none';
          }

          previewTotal.textContent = 'R' + total.toLocaleString('en-ZA', { minimumFractionDigits: 2 });
        } else {
          costPreview.style.display = 'none';
        }
      }

      function updateAmountHelp() {
        if (multiplyCheckbox.checked) {
          amountHelp.textContent = `Enter amount per learner (will be multiplied by ${totalLearners.toLocaleString()} learners)`;
          amountHelp.style.color = 'var(--primary)';
          amountHelp.style.fontWeight = '500';
        } else {
          amountHelp.textContent = 'Enter the base amount per unit';
          amountHelp.style.color = 'var(--text-secondary)';
          amountHelp.style.fontWeight = 'normal';
        }
      }

      // Event listeners
      amountInput.addEventListener('input', updateCostPreview);
      daysInput.addEventListener('input', updateCostPreview);
      multiplyCheckbox.addEventListener('change', () => {
        updateCostPreview();
        updateAmountHelp();
      });

      // Initialize
      updateAmountHelp();

      // Update form submission to include the calculated amount
      const costForm = document.getElementById('cost-form');
      costForm.addEventListener('submit', (e) => {
        if (multiplyCheckbox.checked) {
          // Add a hidden field with the multiplier info
          const multiplierField = document.createElement('input');
          multiplierField.type = 'hidden';
          multiplierField.name = 'learner_multiplier';
          multiplierField.value = totalLearners;
          costForm.appendChild(multiplierField);

          // Update the amount to reflect the total
          const originalAmount = parseFloat(amountInput.value) || 0;
          const days = parseInt(daysInput.value) || 1;
          const totalAmount = originalAmount * days * totalLearners;

          // Add original amount for reference
          const originalAmountField = document.createElement('input');
          originalAmountField.type = 'hidden';
          originalAmountField.name = 'original_amount';
          originalAmountField.value = originalAmount;
          costForm.appendChild(originalAmountField);

          // Update the main amount field to the calculated total
          amountInput.value = totalAmount;

          // Set days to 1 since we've already calculated the total
          daysInput.value = 1;
        }
      });
    });
  </script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
