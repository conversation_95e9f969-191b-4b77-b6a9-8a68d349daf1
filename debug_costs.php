<?php
// Debug script to check costs data
require_once __DIR__ . '/config/db.php';

echo "<h1>Cost Calculator Debug Information</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.debug-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.success { color: green; }
.error { color: red; }
.info { color: blue; }
</style>";

// Check database connection
echo "<div class='debug-section'>";
echo "<h2>1. Database Connection</h2>";
try {
    if (isset($pdo)) {
        echo "<p class='success'>✓ Database connection successful (PDO)</p>";
    } else {
        echo "<p class='error'>✗ PDO connection not available</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Check costs data
echo "<div class='debug-section'>";
echo "<h2>2. Costs Data</h2>";
try {
    $costs = $pdo->query("SELECT co.*, ca.name AS category, su.name AS subcategory FROM costs co LEFT JOIN categories ca ON co.category_id = ca.id LEFT JOIN subcategories su ON co.subcategory_id = su.id ORDER BY ca.name, su.name, co.created_at DESC")->fetchAll();
    
    echo "<p class='info'>Total costs found: " . count($costs) . "</p>";
    
    if (count($costs) > 0) {
        echo "<h3>Sample costs:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Description</th><th>Amount</th><th>Category</th><th>Subcategory</th></tr>";
        
        $sampleCosts = array_slice($costs, 0, 5); // Show first 5 costs
        foreach ($sampleCosts as $cost) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($cost['id']) . "</td>";
            echo "<td>" . htmlspecialchars($cost['description']) . "</td>";
            echo "<td>R" . number_format($cost['amount'], 2) . "</td>";
            echo "<td>" . htmlspecialchars($cost['category'] ?? 'No Category') . "</td>";
            echo "<td>" . htmlspecialchars($cost['subcategory'] ?? 'No Subcategory') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>✗ No costs found in database</p>";
        echo "<p>This could be why the dropdown appears blank.</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error fetching costs: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Check categories
echo "<div class='debug-section'>";
echo "<h2>3. Categories</h2>";
try {
    $categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll();
    echo "<p class='info'>Total categories found: " . count($categories) . "</p>";
    
    if (count($categories) > 0) {
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li>" . htmlspecialchars($category['name']) . " (ID: " . $category['id'] . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='error'>✗ No categories found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error fetching categories: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Check subcategories
echo "<div class='debug-section'>";
echo "<h2>4. Subcategories</h2>";
try {
    $subcategories = $pdo->query("SELECT su.*, ca.name as category_name FROM subcategories su LEFT JOIN categories ca ON su.category_id = ca.id ORDER BY ca.name, su.name")->fetchAll();
    echo "<p class='info'>Total subcategories found: " . count($subcategories) . "</p>";
    
    if (count($subcategories) > 0) {
        echo "<ul>";
        foreach ($subcategories as $sub) {
            echo "<li>" . htmlspecialchars($sub['name']) . " (Category: " . htmlspecialchars($sub['category_name'] ?? 'Unknown') . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='error'>✗ No subcategories found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error fetching subcategories: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Group costs by category (same logic as costs.php)
echo "<div class='debug-section'>";
echo "<h2>5. Grouped Costs (Same as costs.php)</h2>";
try {
    $groupedCosts = [];
    foreach ($costs as $cost) {
        $category = $cost['category'] ?? 'Uncategorized';
        $subcategory = $cost['subcategory'] ?? 'No Subcategory';

        if (!isset($groupedCosts[$category])) {
            $groupedCosts[$category] = [];
        }

        if (!isset($groupedCosts[$category][$subcategory])) {
            $groupedCosts[$category][$subcategory] = [];
        }

        $groupedCosts[$category][$subcategory][] = $cost;
    }
    
    echo "<p class='info'>Categories after grouping: " . count($groupedCosts) . "</p>";
    
    if (count($groupedCosts) > 0) {
        foreach ($groupedCosts as $categoryName => $subcategories) {
            $categoryTotal = 0;
            $categoryCount = 0;
            foreach ($subcategories as $subcategoryCosts) {
                foreach ($subcategoryCosts as $cost) {
                    $categoryTotal += ($cost['amount'] * $cost['num_days']);
                    $categoryCount++;
                }
            }
            echo "<p><strong>" . htmlspecialchars($categoryName) . "</strong>: " . $categoryCount . " costs, R" . number_format($categoryTotal, 2) . " total</p>";
            
            foreach ($subcategories as $subcategoryName => $subcategoryCosts) {
                echo "<p style='margin-left: 20px;'>└─ " . htmlspecialchars($subcategoryName) . ": " . count($subcategoryCosts) . " costs</p>";
            }
        }
    } else {
        echo "<p class='error'>✗ No grouped costs - this is why dropdown appears blank</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error grouping costs: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Check database tables exist
echo "<div class='debug-section'>";
echo "<h2>6. Database Tables</h2>";
try {
    $tables = ['categories', 'subcategories', 'costs', 'users', 'revenue'];
    foreach ($tables as $table) {
        try {
            $result = $pdo->query("SELECT COUNT(*) as count FROM $table")->fetch();
            echo "<p class='success'>✓ Table '$table' exists with " . $result['count'] . " records</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Table '$table' error: " . $e->getMessage() . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking tables: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='debug-section'>";
echo "<h2>7. Recommendations</h2>";
if (count($costs) == 0) {
    echo "<p class='error'><strong>Issue Found:</strong> No costs in database</p>";
    echo "<p><strong>Solution:</strong> Add some costs using the cost form in the admin panel</p>";
    echo "<p>1. Go to the costs page</p>";
    echo "<p>2. Fill out the 'Add New Cost' form</p>";
    echo "<p>3. Make sure to select a category and subcategory</p>";
    echo "<p>4. Submit the form</p>";
} else {
    echo "<p class='success'>Costs data looks good. The dropdown should be working.</p>";
    echo "<p>If dropdown still appears blank, it might be a JavaScript or CSS issue.</p>";
}
echo "</div>";

echo "<p><a href='admin/costs.php'>← Back to Costs Page</a></p>";
?>
