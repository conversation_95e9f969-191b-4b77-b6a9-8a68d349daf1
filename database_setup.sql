-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS if0_39163993_cost_calculator;

-- Use the database
USE if0_39163993_cost_calculator;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'viewer') NOT NULL DEFAULT 'viewer',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create subcategories table
CREATE TABLE IF NOT EXISTS subcategories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- Create costs table
CREATE TABLE IF NOT EXISTS costs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description VARCHAR(255) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    category_id INT NOT NULL,
    subcategory_id INT NOT NULL,
    rate_type ENUM('daily', 'monthly') NOT NULL DEFAULT 'daily',
    num_days INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    FOREIGN KEY (subcategory_id) REFERENCES subcategories(id)
);

-- Create revenue table
CREATE TABLE IF NOT EXISTS revenue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    month_year VARCHAR(7) NOT NULL,
    student_type VARCHAR(50) NOT NULL,
    revenue_per_student DECIMAL(10,2) NOT NULL,
    total_students INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create invoices table
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_name VARCHAR(100) NOT NULL,
    course VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    tax DECIMAL(5,2) NOT NULL DEFAULT 0,
    due_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin user (username: admin, password: admin123)
-- The password hash is for 'admin123'
INSERT INTO users (username, password, role)
SELECT 'admin', '$2y$10$8WxmVVVlgLBqtGH1mQp9/.Fy9WQKMKo5pN6vQkiA.eVbVZ2g6QmMi', 'admin'
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin');

-- Insert sample categories
INSERT INTO categories (name, description)
SELECT 'Salaries', 'Staff and instructor salaries'
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Salaries');

INSERT INTO categories (name, description)
SELECT 'Facilities', 'Building rent and maintenance'
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Facilities');

INSERT INTO categories (name, description)
SELECT 'Equipment', 'Learning equipment and supplies'
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Equipment');

INSERT INTO categories (name, description)
SELECT 'Marketing', 'Advertising and promotional activities'
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Marketing');

-- Insert sample subcategories (will only work if categories exist)
-- For Salaries category
INSERT INTO subcategories (category_id, name, description)
SELECT c.id, 'Full-time Staff', 'Permanent employees'
FROM categories c
WHERE c.name = 'Salaries' AND NOT EXISTS (
    SELECT 1 FROM subcategories s 
    WHERE s.category_id = c.id AND s.name = 'Full-time Staff'
);

INSERT INTO subcategories (category_id, name, description)
SELECT c.id, 'Part-time Instructors', 'Hourly instructors'
FROM categories c
WHERE c.name = 'Salaries' AND NOT EXISTS (
    SELECT 1 FROM subcategories s 
    WHERE s.category_id = c.id AND s.name = 'Part-time Instructors'
);

-- For Facilities category
INSERT INTO subcategories (category_id, name, description)
SELECT c.id, 'Rent', 'Monthly building rent'
FROM categories c
WHERE c.name = 'Facilities' AND NOT EXISTS (
    SELECT 1 FROM subcategories s 
    WHERE s.category_id = c.id AND s.name = 'Rent'
);

INSERT INTO subcategories (category_id, name, description)
SELECT c.id, 'Utilities', 'Electricity, water, internet'
FROM categories c
WHERE c.name = 'Facilities' AND NOT EXISTS (
    SELECT 1 FROM subcategories s 
    WHERE s.category_id = c.id AND s.name = 'Utilities'
);

-- For Equipment category
INSERT INTO subcategories (category_id, name, description)
SELECT c.id, 'Computers', 'Laptops and desktops'
FROM categories c
WHERE c.name = 'Equipment' AND NOT EXISTS (
    SELECT 1 FROM subcategories s 
    WHERE s.category_id = c.id AND s.name = 'Computers'
);

INSERT INTO subcategories (category_id, name, description)
SELECT c.id, 'Software', 'Licensed software'
FROM categories c
WHERE c.name = 'Equipment' AND NOT EXISTS (
    SELECT 1 FROM subcategories s 
    WHERE s.category_id = c.id AND s.name = 'Software'
);

-- For Marketing category
INSERT INTO subcategories (category_id, name, description)
SELECT c.id, 'Online Ads', 'Google and social media ads'
FROM categories c
WHERE c.name = 'Marketing' AND NOT EXISTS (
    SELECT 1 FROM subcategories s 
    WHERE s.category_id = c.id AND s.name = 'Online Ads'
);

INSERT INTO subcategories (category_id, name, description)
SELECT c.id, 'Print Materials', 'Brochures and flyers'
FROM categories c
WHERE c.name = 'Marketing' AND NOT EXISTS (
    SELECT 1 FROM subcategories s 
    WHERE s.category_id = c.id AND s.name = 'Print Materials'
);
